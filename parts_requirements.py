"""
Parts Requirements System
Manages required parts for cars and provides validation and default parts
"""

import json
import os

class PartsRequirements:
    def __init__(self):
        # Define which parts are required for each car model
        self.car_specific_requirements = {
            "old": {
                "engine": True,      # Engine is always required
                "intercooler": False, # Old cars don't have intercoolers
                "ecu": True,         # ECU is required
                "turbo": False       # Turbo is optional (but old cars can't have it)
            },
            "future": {
                "engine": True,      # Engine is always required
                "intercooler": True, # Future cars need intercoolers
                "ecu": True,         # ECU is required
                "turbo": True        # Future cars need turbo
            },
            "Xtreme": {
                "engine": True,      # Engine is always required
                "intercooler": True, # Xtreme cars need intercoolers
                "ecu": True,         # ECU is required
                "turbo": True        # Xtreme cars need turbo
            }
        }

        # Fallback requirements for unknown car models
        self.default_requirements = {
            "engine": True,      # Engine is always required
            "intercooler": True, # Intercooler is required by default
            "ecu": True,         # ECU is required
            "turbo": False       # Turbo is optional by default
        }
        
        # Default parts to use when required parts are missing
        self.default_parts = {
            "engine": {
                "name": "Basic I4",
                "horsepower": 120,
                "weight": 180,
                "value": 500,
                "category": "engine"
            },
            "intercooler": {
                "name": "Basic Front Mount",
                "horsepower_boost_percentage": 8,
                "weight": 15,
                "value": 300,
                "category": "intercooler"
            },
            "ecu": {
                "name": "Stage 1 Tune",
                "horsepower_boost_percentage": 10,
                "weight": 2,
                "value": 400,
                "category": "ecu"
            }
        }
    
    def is_part_required(self, part_category, car_name=None):
        """Check if a part category is required for a specific car"""
        if car_name and car_name in self.car_specific_requirements:
            return self.car_specific_requirements[car_name].get(part_category, False)
        return self.default_requirements.get(part_category, False)

    def get_required_parts(self, car_name=None):
        """Get list of all required part categories for a specific car"""
        requirements = self.car_specific_requirements.get(car_name, self.default_requirements)
        return [category for category, required in requirements.items() if required]

    def get_optional_parts(self, car_name=None):
        """Get list of all optional part categories for a specific car"""
        requirements = self.car_specific_requirements.get(car_name, self.default_requirements)
        return [category for category, required in requirements.items() if not required]
    
    def validate_car_parts(self, car_data):
        """
        Validate that a car has all required parts
        Returns: (is_valid, missing_parts, validation_errors)
        """
        parts = car_data.get("parts", {})
        car_name = car_data.get("name", "unknown")
        missing_parts = []
        validation_errors = []

        # Get requirements for this specific car model
        requirements = self.car_specific_requirements.get(car_name, self.default_requirements)

        for category, required in requirements.items():
            if required:
                part = parts.get(category)
                if part is None:
                    missing_parts.append(category)
                    validation_errors.append(f"Missing required part: {category}")
                elif not isinstance(part, dict):
                    validation_errors.append(f"Invalid part data for {category}")
                elif "name" not in part:
                    validation_errors.append(f"Part {category} missing name field")

        is_valid = len(missing_parts) == 0 and len(validation_errors) == 0
        return is_valid, missing_parts, validation_errors
    
    def get_default_part(self, part_category):
        """Get default part for a category"""
        return self.default_parts.get(part_category, None)
    
    def fix_car_parts(self, car_data, add_to_inventory=True):
        """
        Fix car parts by adding default parts for missing required parts
        Returns: (fixed_car_data, parts_added)
        """
        if "parts" not in car_data:
            car_data["parts"] = {}

        car_name = car_data.get("name", "unknown")
        parts_added = []

        # Check and fix each required part for this specific car
        for category in self.get_required_parts(car_name):
            if car_data["parts"].get(category) is None:
                default_part = self.get_default_part(category)
                if default_part:
                    car_data["parts"][category] = default_part.copy()
                    parts_added.append((category, default_part["name"]))

        # Ensure optional parts are at least set to None
        for category in self.get_optional_parts(car_name):
            if category not in car_data["parts"]:
                car_data["parts"][category] = None

        # Add parts to player inventory if requested
        if add_to_inventory and parts_added:
            self._add_parts_to_inventory(parts_added)

        return car_data, parts_added
    
    def _add_parts_to_inventory(self, parts_added):
        """Add parts to player inventory"""
        try:
            # Load profile data
            with open('data/profile.json', 'r') as f:
                profile_data = json.load(f)
            
            # Ensure inventory structure exists
            if "inventory" not in profile_data:
                profile_data["inventory"] = {"owned_cars": [], "owned_parts": {}}
            if "owned_parts" not in profile_data["inventory"]:
                profile_data["inventory"]["owned_parts"] = {}
            
            # Add each part to inventory
            for category, part_name in parts_added:
                if category not in profile_data["inventory"]["owned_parts"]:
                    profile_data["inventory"]["owned_parts"][category] = []
                
                if part_name not in profile_data["inventory"]["owned_parts"][category]:
                    profile_data["inventory"]["owned_parts"][category].append(part_name)
            
            # Save profile data
            with open('data/profile.json', 'w') as f:
                json.dump(profile_data, f, indent=4)
                
        except Exception as e:
            print(f"Error adding parts to inventory: {e}")
    
    def fix_all_cars_in_garage(self):
        """Fix all cars in garage by adding missing required parts"""
        try:
            # Load garage data
            with open('data/garage.json', 'r') as f:
                garage_data = json.load(f)
            
            total_parts_added = []
            cars_fixed = 0
            
            # Fix each car
            for i, car_data in enumerate(garage_data):
                is_valid, missing_parts, _ = self.validate_car_parts(car_data)
                
                if not is_valid:
                    fixed_car, parts_added = self.fix_car_parts(car_data, add_to_inventory=True)
                    garage_data[i] = fixed_car
                    total_parts_added.extend(parts_added)
                    cars_fixed += 1
            
            # Save garage data if any changes were made
            if cars_fixed > 0:
                with open('data/garage.json', 'w') as f:
                    json.dump(garage_data, f, indent=4)
            
            return cars_fixed, total_parts_added
            
        except Exception as e:
            print(f"Error fixing cars in garage: {e}")
            return 0, []
    
    def safe_get_part_value(self, part_data, field, default=0):
        """Safely get a value from part data"""
        if part_data is None or not isinstance(part_data, dict):
            return default
        return part_data.get(field, default)
    
    def safe_calculate_horsepower(self, parts):
        """Safely calculate total horsepower from parts"""
        if not isinstance(parts, dict):
            return 0
        
        # Get base horsepower from engine
        engine = parts.get("engine")
        base_hp = self.safe_get_part_value(engine, "horsepower", 0)
        
        # Calculate boost from other parts
        total_boost = 0
        for part_category, part_data in parts.items():
            if part_category != "engine" and part_data is not None:
                boost = self.safe_get_part_value(part_data, "horsepower_boost_percentage", 0)
                total_boost += boost
        
        # Calculate final horsepower
        final_hp = int(base_hp * (1 + total_boost / 100))
        return final_hp
    
    def safe_calculate_weight(self, base_weight, parts):
        """Safely calculate total weight including parts"""
        if not isinstance(parts, dict):
            return base_weight
        
        parts_weight = 0
        for part_data in parts.values():
            if part_data is not None:
                parts_weight += self.safe_get_part_value(part_data, "weight", 0)
        
        return base_weight + parts_weight

# Global instance
parts_requirements = PartsRequirements()
